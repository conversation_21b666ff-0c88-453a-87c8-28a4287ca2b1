.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;
  padding: 120px 0 80px;

  // Arrière-plan animé moderne
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.3;
      animation: float 8s ease-in-out infinite;

      &.orb-1 {
        width: 300px;
        height: 300px;
        background: linear-gradient(45deg, #6366f1, #8b5cf6);
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg, #06b6d4, #3b82f6);
        top: 60%;
        right: 20%;
        animation-delay: 3s;
      }

      &.orb-3 {
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg, #ec4899, #f59e0b);
        bottom: 20%;
        left: 30%;
        animation-delay: 6s;
      }
    }

    .floating-elements {
      position: absolute;
      width: 100%;
      height: 100%;

      .floating-element {
        position: absolute;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        animation: floatSlow 12s ease-in-out infinite;

        &.element-1 {
          top: 15%;
          left: 15%;
          animation-delay: 0s;
        }

        &.element-2 {
          top: 25%;
          right: 25%;
          animation-delay: 3s;
        }

        &.element-3 {
          bottom: 35%;
          left: 25%;
          animation-delay: 6s;
        }

        &.element-4 {
          bottom: 15%;
          right: 15%;
          animation-delay: 9s;
        }
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .hero-content {
    .hero-badge {
      display: inline-block;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      font-size: 14px;
      font-weight: 500;
      color: white;
      margin-bottom: 32px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
      }
    }

    .hero-title {
      font-size: 3.75rem;
      font-weight: 700;
      line-height: 1.2;
      color: white;
      margin-bottom: 24px;
      letter-spacing: -0.01em;
      font-family: Sora-Bold;
    }

    .hero-subtitle {
      font-size: 1.25rem;
      line-height: 1.7;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 48px;
      max-width: 600px;
      font-weight: 400;
      font-family: Sora-thin;
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      margin-bottom: 64px;

      @media (max-width: 640px) {
        flex-direction: column;
        gap: 16px;
      }

      .btn-primary,
      .btn-secondary {
        padding: 16px 32px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }

      .btn-primary {
        background: #3b82f6;
        color: white;

        &:hover {
          background: #2563eb;
        }
      }

      .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
    .hero-stats {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;

        margin-bottom: 32px;

        @media (max-width: 640px) {
          grid-template-columns: 1fr;
          gap: 24px;
        }

        .stat-item {
          position: relative;

          .stat-number {
            font-size: 2.5rem;
            font-weight: 900;

            color: white;
            line-height: 1;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
            margin-bottom: 12px;
          }

          .stat-progress {
            width: 100%;
            height: 3px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;

            .progress-bar {
              height: 100%;
              background: linear-gradient(90deg, #6366f1, #8b5cf6);
              border-radius: 2px;
              width: 0;
              animation: progressFill 2s ease-out forwards;
              animation-delay: calc(var(--index, 0) * 0.3s + 1s);
            }
          }
        }
      }

      .trust-badges {
        display: flex;
        align-items: center;
        gap: 24px;
        padding-top: 32px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: 16px;
        }

        .trust-text {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
        }

        .badges {
          display: flex;
          gap: 16px;

          .badge-item {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  // Visuel professionnel épuré
  .hero-visual {
    position: relative;

    .dashboard-mockup {
      position: relative;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
      transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
      transition: transform 0.3s ease;

      &:hover {
        transform: perspective(1000px) rotateY(-2deg) rotateX(2deg);
      }

      .dashboard-image {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    @media (max-width: 1024px) {
      .dashboard-mockup {
        transform: none;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }
}

// Animations modernes
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(2deg);
  }
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-25px) translateX(15px) scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes progressFill {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes dashboardFloat {
  0%,
  100% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(0px);
  }
  50% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(-10px);
  }
}

@keyframes cardPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

@keyframes barGrow {
  0% {
    transform: scaleY(0);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes floatCard {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-15px) rotate(1deg);
    opacity: 1;
  }
}
