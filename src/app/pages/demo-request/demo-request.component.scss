/* PAGE DEMANDE DE DÉMO STYLE SEAMLESSHR */
.demo-page {
  min-height: 100vh;
  background: #f8fafc;
}

.demo-hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .hero-text {
    .hero-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      color: #1a1a1a;
      margin-bottom: 24px;

      @media (max-width: 1024px) {
        font-size: 40px;
      }

      @media (max-width: 768px) {
        font-size: 32px;
      }
    }

    .hero-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 20px;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 40px;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }

    .hero-features {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        color: #1a1a1a;

        @media (max-width: 1024px) {
          justify-content: center;
        }

        i {
          color: #4f46e5;
          font-size: 18px;
        }
      }
    }
  }

  .hero-form {
    .form-card {
      background: #ffffff;
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;

      @media (max-width: 768px) {
        padding: 32px 24px;
      }

      .form-title {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 28px;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 32px;
        text-align: center;

        @media (max-width: 768px) {
          font-size: 24px;
        }
      }
    }
  }
}

/* FORMULAIRE */
.demo-form {
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  .form-group {
    margin-bottom: 24px;

    label {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 8px;
      display: block;
    }

    .form-control {
      width: 100%;
      padding: 14px 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 16px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      transition: all 0.3s ease;
      background: #ffffff;

      &:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        outline: none;
      }

      &::placeholder {
        color: #9ca3af;
      }

      &.ng-invalid.ng-touched {
        border-color: #ef4444;
      }
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .error-message {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 12px;
      color: #ef4444;
      margin-top: 6px;
    }
  }

  .checkbox-group {
    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      cursor: pointer;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      line-height: 1.5;

      .checkbox-input {
        display: none;
      }

      .checkbox-custom {
        width: 20px;
        height: 20px;
        border: 2px solid #e5e7eb;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        flex-shrink: 0;
        margin-top: 2px;

        &::after {
          content: "✓";
          color: #ffffff;
          font-size: 12px;
          font-weight: 700;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      .checkbox-text {
        color: #666666;

        .link {
          color: #4f46e5;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      &:hover .checkbox-custom {
        border-color: #4f46e5;
      }
    }

    .checkbox-input:checked + .checkbox-custom {
      background: #4f46e5;
      border-color: #4f46e5;

      &::after {
        opacity: 1;
      }
    }
  }

  .submit-btn {
    width: 100%;
    padding: 16px 24px;
    background: #4f46e5;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;

    &:hover:not(:disabled) {
      background: #3730a3;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    i {
      margin-right: 8px;
    }
  }
}
