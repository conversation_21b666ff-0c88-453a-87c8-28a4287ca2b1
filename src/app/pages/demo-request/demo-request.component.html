<div class="demo-page">
  <!-- Header avec navbar -->
  <app-navbar></app-navbar>

  <!-- Hero Section -->
  <section class="demo-hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">Découvrez LuminaHR en action</h1>
          <p class="hero-subtitle">
            Réservez une démonstration personnalisée et voyez comment LuminaHR
            peut transformer la gestion des ressources humaines de votre
            entreprise.
          </p>
          <div class="hero-features">
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Démonstration personnalisée de 30 minutes</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Analyse gratuite de vos besoins RH</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Réponses à toutes vos questions</span>
            </div>
          </div>
        </div>
        <div class="hero-form">
          <div class="form-card">
            <h2 class="form-title">Demander une démo</h2>
            <form
              [formGroup]="demoForm"
              (ngSubmit)="onSubmit()"
              class="demo-form"
            >
              <div class="form-row">
                <div class="form-group">
                  <label for="firstName">Prénom *</label>
                  <input
                    type="text"
                    id="firstName"
                    formControlName="firstName"
                    class="form-control"
                    placeholder="Votre prénom"
                  />
                  <div
                    *ngIf="
                      demoForm.get('firstName')?.invalid &&
                      demoForm.get('firstName')?.touched
                    "
                    class="error-message"
                  >
                    Le prénom est requis
                  </div>
                </div>
                <div class="form-group">
                  <label for="lastName">Nom *</label>
                  <input
                    type="text"
                    id="lastName"
                    formControlName="lastName"
                    class="form-control"
                    placeholder="Votre nom"
                  />
                  <div
                    *ngIf="
                      demoForm.get('lastName')?.invalid &&
                      demoForm.get('lastName')?.touched
                    "
                    class="error-message"
                  >
                    Le nom est requis
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email professionnel *</label>
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="form-control"
                  placeholder="<EMAIL>"
                />
                <div
                  *ngIf="
                    demoForm.get('email')?.invalid &&
                    demoForm.get('email')?.touched
                  "
                  class="error-message"
                >
                  <span *ngIf="demoForm.get('email')?.errors?.['required']"
                    >L'email est requis</span
                  >
                  <span *ngIf="demoForm.get('email')?.errors?.['email']"
                    >Format d'email invalide</span
                  >
                </div>
              </div>

              <div class="form-group">
                <label for="phone">Téléphone *</label>
                <input
                  type="tel"
                  id="phone"
                  formControlName="phone"
                  class="form-control"
                  placeholder="+243 XXX XXX XXX"
                />
                <div
                  *ngIf="
                    demoForm.get('phone')?.invalid &&
                    demoForm.get('phone')?.touched
                  "
                  class="error-message"
                >
                  Le téléphone est requis
                </div>
              </div>

              <div class="form-group">
                <label for="company">Entreprise *</label>
                <input
                  type="text"
                  id="company"
                  formControlName="company"
                  class="form-control"
                  placeholder="Nom de votre entreprise"
                />
                <div
                  *ngIf="
                    demoForm.get('company')?.invalid &&
                    demoForm.get('company')?.touched
                  "
                  class="error-message"
                >
                  Le nom de l'entreprise est requis
                </div>
              </div>

              <div class="form-group">
                <label for="jobTitle">Poste *</label>
                <input
                  type="text"
                  id="jobTitle"
                  formControlName="jobTitle"
                  class="form-control"
                  placeholder="Votre fonction dans l'entreprise"
                />
                <div
                  *ngIf="
                    demoForm.get('jobTitle')?.invalid &&
                    demoForm.get('jobTitle')?.touched
                  "
                  class="error-message"
                >
                  Le poste est requis
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="employeeCount">Nombre d'employés *</label>
                  <select
                    id="employeeCount"
                    formControlName="employeeCount"
                    class="form-control"
                  >
                    <option value="">Sélectionnez</option>
                    <option value="1-10">1-10 employés</option>
                    <option value="11-50">11-50 employés</option>
                    <option value="51-200">51-200 employés</option>
                    <option value="201-500">201-500 employés</option>
                    <option value="500+">500+ employés</option>
                  </select>
                  <div
                    *ngIf="
                      demoForm.get('employeeCount')?.invalid &&
                      demoForm.get('employeeCount')?.touched
                    "
                    class="error-message"
                  >
                    Veuillez sélectionner le nombre d'employés
                  </div>
                </div>
                <div class="form-group">
                  <label for="industry">Secteur d'activité</label>
                  <select
                    id="industry"
                    formControlName="industry"
                    class="form-control"
                  >
                    <option value="">Sélectionnez</option>
                    <option value="technology">Technologie</option>
                    <option value="finance">Finance</option>
                    <option value="healthcare">Santé</option>
                    <option value="education">Éducation</option>
                    <option value="manufacturing">Industrie</option>
                    <option value="retail">Commerce</option>
                    <option value="other">Autre</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="message">Message (optionnel)</label>
                <textarea
                  id="message"
                  formControlName="message"
                  class="form-control"
                  rows="4"
                  placeholder="Parlez-nous de vos besoins spécifiques..."
                ></textarea>
              </div>

              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    formControlName="acceptTerms"
                    class="checkbox-input"
                  />
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-text">
                    J'accepte les
                    <a href="#" class="link">conditions d'utilisation</a> et la
                    <a href="#" class="link">politique de confidentialité</a> *
                  </span>
                </label>
                <div
                  *ngIf="
                    demoForm.get('acceptTerms')?.invalid &&
                    demoForm.get('acceptTerms')?.touched
                  "
                  class="error-message"
                >
                  Vous devez accepter les conditions
                </div>
              </div>

              <button
                type="submit"
                class="submit-btn"
                [disabled]="demoForm.invalid || isSubmitting"
              >
                <span *ngIf="!isSubmitting">Demander ma démo gratuite</span>
                <span *ngIf="isSubmitting">
                  <i class="fas fa-spinner fa-spin"></i> Envoi en cours...
                </span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
  <app-footer></app-footer>
</div>
