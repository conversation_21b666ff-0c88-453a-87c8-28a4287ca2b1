<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient pour les éléments RH -->
    <linearGradient id="hrGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(99,102,241,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(139,92,246,0.05);stop-opacity:1" />
    </linearGradient>
    
    <!-- Pattern d'organigramme -->
    <pattern id="orgChart" width="200" height="150" patternUnits="userSpaceOnUse">
      <!-- Boîtes d'organigramme -->
      <rect x="75" y="10" width="50" height="20" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1" rx="3"/>
      <rect x="25" y="60" width="40" height="15" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1" rx="2"/>
      <rect x="125" y="60" width="40" height="15" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1" rx="2"/>
      <rect x="75" y="110" width="50" height="20" fill="none" stroke="rgba(255,255,255,0.04)" stroke-width="1" rx="3"/>
      
      <!-- Lignes de connexion -->
      <line x1="100" y1="30" x2="100" y2="45" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="70" y1="45" x2="130" y2="45" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="45" y1="45" x2="45" y2="60" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="145" y1="45" x2="145" y2="60" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="100" y1="75" x2="100" y2="110" stroke="rgba(255,255,255,0.04)" stroke-width="1"/>
    </pattern>
    
    <!-- Pattern de graphiques RH -->
    <pattern id="hrCharts" width="180" height="120" patternUnits="userSpaceOnUse">
      <!-- Graphique en barres -->
      <rect x="20" y="80" width="8" height="30" fill="rgba(99,102,241,0.08)" rx="1"/>
      <rect x="32" y="70" width="8" height="40" fill="rgba(139,92,246,0.06)" rx="1"/>
      <rect x="44" y="85" width="8" height="25" fill="rgba(99,102,241,0.04)" rx="1"/>
      
      <!-- Graphique circulaire -->
      <circle cx="130" cy="60" r="25" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="2" stroke-dasharray="40 20"/>
      <circle cx="130" cy="60" r="20" fill="none" stroke="rgba(99,102,241,0.08)" stroke-width="2" stroke-dasharray="30 40"/>
    </pattern>
    
    <!-- Icônes RH flottantes -->
    <g id="userIcon">
      <circle cx="0" cy="0" r="8" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1.5"/>
      <circle cx="0" cy="-3" r="3" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1"/>
      <path d="M-6,6 Q0,2 6,6" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1"/>
    </g>
    
    <g id="teamIcon">
      <circle cx="-8" cy="0" r="6" fill="none" stroke="rgba(255,255,255,0.04)" stroke-width="1"/>
      <circle cx="0" cy="0" r="6" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1"/>
      <circle cx="8" cy="0" r="6" fill="none" stroke="rgba(255,255,255,0.04)" stroke-width="1"/>
    </g>
  </defs>
  
  <!-- Background base -->
  <rect width="100%" height="100%" fill="url(#hrGradient)"/>
  
  <!-- Patterns d'organigramme -->
  <rect width="100%" height="100%" fill="url(#orgChart)" opacity="0.6"/>
  
  <!-- Patterns de graphiques -->
  <rect width="100%" height="100%" fill="url(#hrCharts)" opacity="0.4"/>
  
  <!-- Icônes flottantes -->
  <use href="#userIcon" x="300" y="200" opacity="0.1">
    <animateTransform attributeName="transform" type="translate" values="0,0; 10,-5; 0,0" dur="8s" repeatCount="indefinite"/>
  </use>
  <use href="#teamIcon" x="800" y="400" opacity="0.08">
    <animateTransform attributeName="transform" type="translate" values="0,0; -8,8; 0,0" dur="12s" repeatCount="indefinite"/>
  </use>
  <use href="#userIcon" x="1400" y="300" opacity="0.06">
    <animateTransform attributeName="transform" type="translate" values="0,0; 5,10; 0,0" dur="10s" repeatCount="indefinite"/>
  </use>
  <use href="#teamIcon" x="500" y="700" opacity="0.05">
    <animateTransform attributeName="transform" type="translate" values="0,0; 12,-3; 0,0" dur="15s" repeatCount="indefinite"/>
  </use>
  
  <!-- Lignes de connexion animées -->
  <path d="M100,200 Q400,100 700,300 T1300,400" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1" stroke-dasharray="5,10">
    <animate attributeName="stroke-dashoffset" values="0;15;0" dur="20s" repeatCount="indefinite"/>
  </path>
  <path d="M200,600 Q600,500 1000,700 T1600,600" fill="none" stroke="rgba(99,102,241,0.05)" stroke-width="1" stroke-dasharray="8,12">
    <animate attributeName="stroke-dashoffset" values="0;20;0" dur="25s" repeatCount="indefinite"/>
  </path>
</svg>
