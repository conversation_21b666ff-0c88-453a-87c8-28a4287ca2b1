/* SECTION TÉMOIGNAGES CLIENTS STYLE SEAMLESSHR */
.customer-stories-section {
  padding: 100px 0;
  background: #f8fafc;

  @media (max-width: 768px) {
    padding: 80px 0;
  }
}

/* === EN-TÊTE STYLE SEAMLESSHR === */
.section-header {
  text-align: center;
  margin-bottom: 80px;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    margin-bottom: 60px;
  }

  .section-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    margin: 0;

    @media (max-width: 1024px) {
      font-size: 40px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }
}

/* === GRILLE DE SUCCESS STORIES === */
.stories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* === CARTES DE SUCCESS STORIES === */
.story-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 24px 20px;
  }

  .company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    height: 60px;

    .logo-image {
      max-width: 120px;
      max-height: 60px;
      width: auto;
      height: auto;
      object-fit: contain;
      filter: grayscale(100%) opacity(0.8);
      transition: filter 0.3s ease;
    }

    @media (max-width: 768px) {
      height: 50px;
      margin-bottom: 20px;

      .logo-image {
        max-width: 100px;
        max-height: 50px;
      }
    }
  }

  &:hover .company-logo .logo-image {
    filter: grayscale(0%) opacity(1);
  }

  .story-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    color: #1a1a1a;
    margin-bottom: 20px;
    flex-grow: 1;

    @media (max-width: 768px) {
      font-size: 16px;
      margin-bottom: 16px;
    }
  }

  .story-link {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #4f46e5;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
    margin-top: auto;

    &:hover {
      color: #3730a3;
    }

    &::after {
      content: "→";
      margin-left: 8px;
      transition: transform 0.3s ease;
    }

    &:hover::after {
      transform: translateX(4px);
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
