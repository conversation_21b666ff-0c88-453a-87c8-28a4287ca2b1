import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { NavbarComponent } from './components/navbar/navbar.component';
import { HeroComponent } from './components/hero/hero.component';
import { ProblemSolutionComponent } from './components/problem-solution/problem-solution.component';
import { ProductsTabsComponent } from './components/products-tabs/products-tabs.component';
import { IntegrationsComponent } from './components/integrations/integrations.component';
import { SecurityComponent } from './components/security/security.component';
import { CustomerStoriesComponent } from './components/customer-stories/customer-stories.component';
import { JobsComponent } from './components/jobs/jobs.component';
import { FinalCtaComponent } from './components/final-cta/final-cta.component';
import { FooterComponent } from './components/footer/footer.component';
import { DemoRequestComponent } from './pages/demo-request/demo-request.component';
import { JobDetailComponent } from './pages/job-detail/job-detail.component';
import { PayrollSoftwareComponent } from './pages/payroll-software/payroll-software.component';
import { HomeComponent } from './pages/home/<USER>';
import { SirhComponent } from './pages/sirh/sirh.component';
import { PaieComponent } from './pages/paie/paie.component';
import { PerformanceComponent } from './pages/performance/performance.component';
import { RecrutementComponent } from './pages/recrutement/recrutement.component';
import { GestionTempsComponent } from './pages/gestion-temps/gestion-temps.component';
import { AvantagesEmployesComponent } from './pages/avantages-employes/avantages-employes.component';
import { PricingComponent } from './components/pricing/pricing.component';

@NgModule({
  declarations: [
    AppComponent,
    NavbarComponent,
    HeroComponent,
    ProblemSolutionComponent,
    ProductsTabsComponent,
    IntegrationsComponent,
    SecurityComponent,
    CustomerStoriesComponent,
    JobsComponent,
    FinalCtaComponent,
    FooterComponent,
    DemoRequestComponent,
    JobDetailComponent,
    PayrollSoftwareComponent,
    HomeComponent,
    SirhComponent,
    PaieComponent,
    PerformanceComponent,
    RecrutementComponent,
    GestionTempsComponent,
    AvantagesEmployesComponent,
    PricingComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
