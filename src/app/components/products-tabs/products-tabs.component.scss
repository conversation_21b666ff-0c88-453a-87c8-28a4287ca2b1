/* SECTION PRODUITS MODERNE */
.products-section {
  padding: 100px 0;
  background: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;

  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: #1f2937;
    margin-bottom: 16px;
    letter-spacing: -0.01em;

    @media (max-width: 1024px) {
      font-size: 2.25rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: 1rem;
      line-height: 1.5;
    }
  }
}

/* === ONGLETS DE NAVIGATION - STYLE TWENTY === */
.products-tabs {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 80px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 8px;
    margin-bottom: 60px;
  }

  .tab-button {
    padding: 12px 24px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #d1d5db;
      background: #f9fafb;
    }

    &.active {
      background: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }

    @media (max-width: 768px) {
      padding: 10px 20px;
      font-size: 13px;
    }
  }
}

/* === CONTENU DES ONGLETS === */
.tab-content {
  .tab-panel {
    display: none;

    &.active {
      display: block;
    }
  }

  .product-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .product-text {
    .product-title {
      font-size: 1.875rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 16px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .product-description {
      font-size: 1rem;
      line-height: 1.6;
      color: #6b7280;
      margin-bottom: 24px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0 0 32px 0;

      li {
        padding: 8px 0;
        color: #374151;
        font-size: 14px;
        position: relative;
        padding-left: 24px;

        &::before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #10b981;
          font-weight: 600;
        }
      }
    }

    .learn-more-btn {
      display: inline-block;
      color: #3b82f6;
      font-weight: 500;
      text-decoration: none;
      font-size: 14px;
      transition: all 0.3s ease;

      &:hover {
        color: #1d4ed8;
        text-decoration: underline;
      }
    }
  }

  .product-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .product-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--gray-200);
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .products-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
