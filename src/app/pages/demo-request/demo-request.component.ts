import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-demo-request',
  templateUrl: './demo-request.component.html',
  styleUrls: ['./demo-request.component.scss'],
})
export class DemoRequestComponent implements OnInit {
  demoForm!: FormGroup;
  isSubmitting = false;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.demoForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      company: ['', [Validators.required]],
      jobTitle: ['', [Validators.required]],
      employeeCount: ['', [Validators.required]],
      industry: [''],
      message: [''],
      acceptTerms: [false, [Validators.requiredTrue]],
    });
  }

  onSubmit(): void {
    if (this.demoForm.valid) {
      this.isSubmitting = true;

      // Simuler l'envoi de la demande
      setTimeout(() => {
        console.log('Demande de démo envoyée:', this.demoForm.value);
        alert(
          'Votre demande de démo a été envoyée avec succès ! Nous vous contactons sous 24h.'
        );
        this.isSubmitting = false;
        this.demoForm.reset();
      }, 2000);
    } else {
      // Marquer tous les champs comme touchés pour afficher les erreurs
      Object.keys(this.demoForm.controls).forEach((key) => {
        this.demoForm.get(key)?.markAsTouched();
      });
    }
  }
}
