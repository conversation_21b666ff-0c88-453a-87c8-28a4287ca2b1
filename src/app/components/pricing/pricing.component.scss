/* === SECTION TARIFICATION STYLE SEAMLESSHR === */
.pricing-section {
  padding: 100px 0;
  background: #f8fafc;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  /* En-tête */
  .section-header {
    text-align: center;
    margin-bottom: 80px;

    .section-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 48px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 16px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 36px;
      }
    }

    .section-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 20px;
      line-height: 1.6;
      color: #666666;
      max-width: 600px;
      margin: 0 auto;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }
  }

  /* Grille de tarification */
  .pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-bottom: 80px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 24px;
      max-width: 400px;
      margin: 0 auto 80px;
    }
  }

  /* Cartes de tarification */
  .pricing-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 40px 32px;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border-color: #4f46e5;
    }

    &.featured {
      border-color: #4f46e5;
      transform: scale(1.05);
      box-shadow: 0 20px 40px rgba(79, 70, 229, 0.15);

      @media (max-width: 1024px) {
        transform: none;
      }

      &:hover {
        transform: scale(1.05) translateY(-4px);

        @media (max-width: 1024px) {
          transform: translateY(-4px);
        }
      }
    }

    .popular-badge {
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
      color: #ffffff;
      padding: 8px 24px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .plan-header {
      text-align: center;
      margin-bottom: 32px;

      .plan-name {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 24px;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 8px;
      }

      .plan-description {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        color: #666666;
        margin-bottom: 24px;
      }

      .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 4px;

        .currency {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 24px;
          font-weight: 600;
          color: #4f46e5;
        }

        .amount {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 48px;
          font-weight: 700;
          color: #1a1a1a;
          line-height: 1;
        }

        .period {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 16px;
          color: #666666;
          font-weight: 500;
        }
      }
    }

    .plan-features {
      flex: 1;
      margin-bottom: 32px;

      .features-title {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 20px;
      }

      .features-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .feature-item {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

          &:last-child {
            margin-bottom: 0;
          }

          i {
            color: #4f46e5;
            font-size: 16px;
            flex-shrink: 0;
          }

          span {
            font-size: 15px;
            color: #1a1a1a;
            line-height: 1.4;
          }
        }
      }
    }

    .plan-action {
      .plan-btn {
        display: block;
        width: 100%;
        padding: 16px 24px;
        text-align: center;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &.plan-btn-primary {
          background: #4f46e5;
          color: #ffffff;
          border: 2px solid #4f46e5;

          &:hover {
            background: #3730a3;
            border-color: #3730a3;
            transform: translateY(-2px);
          }
        }

        &.plan-btn-secondary {
          background: #ffffff;
          color: #4f46e5;
          border: 2px solid #4f46e5;

          &:hover {
            background: #4f46e5;
            color: #ffffff;
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  /* Section FAQ */
  .pricing-faq {
    margin-bottom: 80px;

    .faq-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 32px;
      font-weight: 700;
      color: #1a1a1a;
      text-align: center;
      margin-bottom: 48px;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .faq-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 32px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .faq-item {
        background: #ffffff;
        padding: 24px;
        border-radius: 12px;
        border: 1px solid #e5e7eb;

        .faq-question {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 18px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 12px;
        }

        .faq-answer {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 15px;
          line-height: 1.6;
          color: #666666;
          margin: 0;
        }
      }
    }
  }

  /* CTA final */
  .pricing-cta {
    text-align: center;
    background: #ffffff;
    padding: 48px 32px;
    border-radius: 16px;
    border: 1px solid #e5e7eb;

    .cta-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 32px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 12px;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .cta-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 32px;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }

    .cta-actions {
      display: flex;
      gap: 16px;
      justify-content: center;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
      }

      .cta-btn-primary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: #4f46e5;
        color: #ffffff;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #3730a3;
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }
      }

      .cta-btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: #ffffff;
        color: #4f46e5;
        text-decoration: none;
        border: 2px solid #4f46e5;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #4f46e5;
          color: #ffffff;
          transform: translateY(-2px);
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}
