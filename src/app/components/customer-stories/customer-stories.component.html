<!-- SECTION TÉMOIGNAGES CLIENTS STYLE SEAMLESSHR -->
<section class="customer-stories-section">
  <div class="container">
    <!-- En-tête style SeamlessHR -->
    <div class="section-header" data-aos="fade-up">
      <h2 class="section-title">
        Nous aidons des entreprises comme la vôtre à devenir
        <span class="highlight-text">meilleures, plus intelligentes</span> et
        transparentes
      </h2>
    </div>

    <!-- Carrousel de success stories -->
    <div class="stories-carousel" data-aos="fade-up" data-aos-delay="200">
      <div class="carousel-container">
        <div
          class="carousel-track"
          [style.transform]="'translateX(' + -currentSlide * 100 + '%)'"
        >
          <!-- Story 1 -->
          <div class="story-slide">
            <div class="story-card">
              <div class="company-logo">
                <img
                  src="assets/images/companies/investment-one.png"
                  alt="Investment One"
                  class="logo-image"
                />
              </div>
              <h3 class="story-title">
                Investment One rationalise les opérations RH et améliore
                l'efficacité de la paie
              </h3>
              <a href="#" class="story-link">Lire l'histoire</a>
            </div>
          </div>

          <!-- Story 2 -->
          <div class="story-slide">
            <div class="story-card">
              <div class="company-logo">
                <img
                  src="assets/images/companies/access-pensions.png"
                  alt="Access Pensions"
                  class="logo-image"
                />
              </div>
              <h3 class="story-title">
                Access Pensions améliore la satisfaction des employés avec
                LuminaHR
              </h3>
              <a href="#" class="story-link">Lire l'histoire</a>
            </div>
          </div>

          <!-- Story 3 -->
          <div class="story-slide">
            <div class="story-card">
              <div class="company-logo">
                <img
                  src="assets/images/companies/churchgate.png"
                  alt="Churchgate Group"
                  class="logo-image"
                />
              </div>
              <h3 class="story-title">
                Churchgate Group gère les performances avec LuminaHR
              </h3>
              <a href="#" class="story-link">Lire l'histoire</a>
            </div>
          </div>
        </div>

        <!-- Navigation dots -->
        <div class="carousel-dots">
          <button
            *ngFor="let slide of [0, 1, 2]; let i = index"
            class="dot"
            [class.active]="currentSlide === i"
            (click)="goToSlide(i)"
          ></button>
        </div>
      </div>
    </div>
  </div>
</section>
