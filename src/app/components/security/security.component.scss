/* SECTION SÉCURITÉ STYLE SEAMLESSHR */
.security-section {
  padding: 100px 0;
  background: #f8fafc;

  @media (max-width: 768px) {
    padding: 80px 0;
  }
}

/* === SECTION EXPANSION GLOBALE === */
.global-expansion {
  margin-bottom: 100px;

  @media (max-width: 768px) {
    margin-bottom: 80px;
  }

  .global-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .global-text {
    .section-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      color: #1a1a1a;
      margin-bottom: 24px;

      @media (max-width: 1024px) {
        font-size: 40px;
      }

      @media (max-width: 768px) {
        font-size: 32px;
      }
    }

    .section-description {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      line-height: 1.6;
      color: #666666;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }
  }

  .global-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .global-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 12px;
    }
  }
}

/* === SECTION SÉCURITÉ FORTERESSE === */
.fortress-security {
  max-width: 800px;
  margin: 0 auto;

  .security-card {
    background: linear-gradient(135deg, #4338ca 0%, #3730a3 100%);
    border-radius: 20px;
    padding: 48px;
    color: white;
    position: relative;
    overflow: hidden;

    @media (max-width: 768px) {
      padding: 32px 24px;
    }

    .security-content {
      margin-bottom: 40px;

      .security-title {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 32px;
        font-weight: 700;
        line-height: 1.2;
        color: white;
        margin-bottom: 16px;

        @media (max-width: 768px) {
          font-size: 24px;
        }
      }

      .security-description {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.9);
        max-width: 500px;

        @media (max-width: 768px) {
          font-size: 14px;
        }
      }
    }
  }
}

/* === BADGES DE CERTIFICATION === */
.certifications-badges {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 16px;
    justify-content: center;
  }
}

.certification-badge {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 140px;

  @media (max-width: 768px) {
    padding: 16px;
    min-width: 120px;
    gap: 12px;
  }

  .badge-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 768px) {
      width: 40px;
      height: 40px;
    }

    .stars-circle {
      width: 48px;
      height: 48px;
      position: relative;
      background: #4338ca;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      @media (max-width: 768px) {
        width: 40px;
        height: 40px;
      }

      .star {
        position: absolute;
        width: 3px;
        height: 3px;
        background: white;
        border-radius: 50%;

        &:nth-child(1) {
          top: 8px;
          left: 22px;
        }
        &:nth-child(2) {
          top: 12px;
          left: 32px;
        }
        &:nth-child(3) {
          top: 20px;
          left: 38px;
        }
        &:nth-child(4) {
          top: 30px;
          left: 38px;
        }
        &:nth-child(5) {
          top: 38px;
          left: 32px;
        }
        &:nth-child(6) {
          top: 42px;
          left: 22px;
        }
        &:nth-child(7) {
          top: 38px;
          left: 12px;
        }
        &:nth-child(8) {
          top: 30px;
          left: 6px;
        }
        &:nth-child(9) {
          top: 20px;
          left: 6px;
        }
        &:nth-child(10) {
          top: 12px;
          left: 12px;
        }
        &:nth-child(11) {
          top: 22px;
          left: 22px;
        }
        &:nth-child(12) {
          top: 26px;
          left: 22px;
        }
      }
    }

    .iso-logo {
      width: 48px;
      height: 48px;
      background: #1a1a1a;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      @media (max-width: 768px) {
        width: 40px;
        height: 40px;
      }

      .iso-text {
        color: white;
        font-weight: 700;
        font-size: 12px;

        @media (max-width: 768px) {
          font-size: 10px;
        }
      }

      .iso-circle {
        position: absolute;
        width: 20px;
        height: 20px;
        border: 2px solid white;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        @media (max-width: 768px) {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .badge-text {
    .badge-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 4px;

      @media (max-width: 768px) {
        font-size: 14px;
      }
    }

    .badge-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 12px;
      font-weight: 500;
      color: #666666;
      margin: 0;

      @media (max-width: 768px) {
        font-size: 11px;
      }
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
