import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { DemoRequestComponent } from './pages/demo-request/demo-request.component';
import { JobDetailComponent } from './pages/job-detail/job-detail.component';
import { PayrollSoftwareComponent } from './pages/payroll-software/payroll-software.component';

const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent },
  { path: 'demo', component: DemoRequestComponent },
  { path: 'job/:id', component: JobDetailComponent },
  { path: 'payroll-software', component: PayrollSoftwareComponent },
  { path: '**', redirectTo: '/home' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
      anchorScrolling: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
