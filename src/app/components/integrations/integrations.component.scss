/* SECTION INTÉGRATIONS */
.integrations-section {
  padding: 100px 0;
  background: white;
}

.integrations-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
}

/* === PARTIE TEXTE SEAMLESSHR STYLE === */
.integrations-text {
  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: #0f172a;
    margin-bottom: 16px;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 2.25rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 24px;
    line-height: 1.3;

    @media (max-width: 768px) {
      font-size: 1.25rem;
    }
  }

  .section-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 32px;

    @media (max-width: 768px) {
      font-size: 1rem;
      line-height: 1.5;
    }
  }

  .integration-features {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .feature-item {
      color: #374151;
      font-weight: 500;
      font-size: 14px;
      position: relative;
      padding-left: 20px;

      &::before {
        content: "•";
        position: absolute;
        left: 0;
        color: #3b82f6;
        font-weight: 600;
      }
    }
  }
}

/* === LOGOS DES INTÉGRATIONS === */
.integrations-logos {
  .logos-grid {
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    padding: 60px 20px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
      padding: 40px 10px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .central-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    background: #3b82f6;
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);

    .hub-label {
      color: white;
    }
  }

  .logo-item {
    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      background: #f8fafc;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: white;
        border-color: #3b82f6;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      span {
        font-weight: 500;
        color: #374151;
        font-size: 14px;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-integrations {
    padding: 80px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
