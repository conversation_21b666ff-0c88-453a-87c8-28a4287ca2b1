<section class="jobs-section" id="offres-emploi">
  <div class="bg-effects">
    <div class="bg-circle circle-1"></div>
    <div class="bg-circle circle-2"></div>
    <div class="bg-grid"></div>
  </div>

  <div class="container">
    <!-- Header Section -->
    <div class="section-header">
      <h2 class="section-title">Opportunités d'Emploi</h2>
      <p class="section-subtitle">
        Explorez nos dernières offres et trouvez le poste parfait pour faire
        avancer votre carrière
      </p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i> Chargement des offres...
    </div>

    <!-- Interactive Filter -->
    <div *ngIf="!isLoading" class="job-filters">
      <div class="filter-tabs">
        <button
          *ngFor="let cat of jobCategories"
          class="filter-tab"
          [class.active]="currentFilter === cat.id"
          (click)="setCurrentFilter(cat.id)"
        >
          {{ cat.label }}
        </button>
      </div>

      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="Rechercher par mot-clé..."
          [(ngModel)]="searchTerm"
          (input)="filterJobs()"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && filteredOffers.length === 0" class="empty-state">
      <i class="fas fa-briefcase"></i>
      <p>Aucune offre ne correspond à vos critères de recherche.</p>
    </div>

    <!-- Job Listings Grid -->
    <div *ngIf="!isLoading && filteredOffers.length > 0" class="job-grid">
      <div
        *ngFor="let offer of filteredOffers | paginate : config"
        class="job-card"
      >
        <div
          class="job-badge"
          [class.full-time]="offer.contractTypes.includes('FULL_TIME')"
        >
          {{ getContractTypeLabel(offer.contractTypes[0]) }}
        </div>

        <div class="job-main">
          <div class="job-header">
            <h3 class="job-title">{{ offer.title }}</h3>
            <p class="job-date">
              Publié {{ formatPublishDate(offer.publishDate) }}
            </p>
          </div>

          <div class="company-info">
            <div class="company-logo">
              <img
                [src]="
                  offer.company.logo || 'assets/images/default-company.png'
                "
                [alt]="offer.company.companyName"
              />
            </div>
            <div class="company-details">
              <h4>{{ offer.company.companyName }}</h4>
              <div class="job-meta">
                <span class="job-location">
                  <i class="fas fa-map-marker-alt"></i>
                  {{ offer.location }}
                </span>
                <span class="job-salary">
                  <i class="fas fa-coins"></i>
                  {{ formatSalary(offer.minSalary, offer.maxSalary) }}
                </span>
              </div>
            </div>
          </div>

          <div class="job-skills">
            <span
              *ngFor="let skill of offer.requiredSkills.slice(0, 3)"
              class="skill-tag"
            >
              {{ skill }}
            </span>
            <span
              *ngIf="offer.requiredSkills.length > 3"
              class="skill-tag-more"
            >
              +{{ offer.requiredSkills.length - 3 }} autres
            </span>
          </div>
        </div>

        <div class="job-footer">
          <button class="apply-btn" (click)="openApplyModal(offer)">
            Postuler
          </button>
          <button class="details-btn" (click)="navigateToJobDetail(offer.id)">
            Voir détails
          </button>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div
      *ngIf="!isLoading && filteredOffers.length > config.itemsPerPage"
      class="pagination-wrapper"
    >
      <pagination-controls
        (pageChange)="onPageChange($event)"
        previousLabel="Précédent"
        nextLabel="Suivant"
        class="custom-pagination"
      ></pagination-controls>
    </div>
  </div>
</section>
