/* SECTION INTÉGRATIONS STYLE SEAMLESSHR */
.integrations-section {
  padding: 100px 0;
  background: #ffffff;

  @media (max-width: 768px) {
    padding: 80px 0;
  }
}

/* === EN-TÊTE STYLE SEAMLESSHR === */
.section-header {
  text-align: center;
  margin-bottom: 80px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    margin-bottom: 60px;
  }

  .section-subtitle {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: #666666;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .section-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 24px;

    @media (max-width: 1024px) {
      font-size: 40px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }

  .section-description {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 18px;
    line-height: 1.6;
    color: #666666;
    max-width: 700px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
}

/* === GRILLE DE LOGOS STYLE SEAMLESSHR === */
.integrations-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

.integration-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-height: 80px;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }

  .logo-image {
    max-width: 100%;
    max-height: 40px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(100%) opacity(0.7);
    transition: filter 0.3s ease;
  }

  &:hover .logo-image {
    filter: grayscale(0%) opacity(1);
  }

  @media (max-width: 768px) {
    padding: 20px;
    min-height: 70px;

    .logo-image {
      max-height: 32px;
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
