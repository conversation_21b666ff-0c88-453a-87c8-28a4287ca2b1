/* SECTION PRODUITS STYLE SEAMLESSHR */
.products-section {
  padding: 100px 0;
  background: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;

  .section-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 0;

    @media (max-width: 1024px) {
      font-size: 40px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }
}

/* === ONGLETS DE NAVIGATION STYLE SEAMLESSHR === */
.products-tabs {
  display: flex;
  justify-content: center;
  gap: 0;
  margin-bottom: 80px;
  flex-wrap: wrap;
  background: #f8fafc;
  border-radius: 12px;
  padding: 8px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 80px;

  @media (max-width: 768px) {
    margin-bottom: 60px;
    flex-direction: column;
    gap: 4px;
  }

  .tab-button {
    padding: 16px 24px;
    background: transparent;
    border: none;
    border-radius: 8px;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      background: #ffffff;
      color: #1a1a1a;
    }

    &.active {
      background: #ffffff;
      color: #1a1a1a;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
      padding: 12px 20px;
      font-size: 13px;
      text-align: center;
    }
  }
}

/* === CONTENU DES ONGLETS STYLE SEAMLESSHR === */
.tab-content {
  .tab-panel {
    display: none;

    &.active {
      display: block;
    }
  }

  .product-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .product-text {
    .product-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 32px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 8px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .product-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 20px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 24px;
      line-height: 1.3;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }

    .product-description {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 24px;
    }

    .product-note {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      color: #999999;
      margin-bottom: 24px;
      font-style: italic;
    }

    .learn-more-btn {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      display: inline-flex;
      align-items: center;
      color: #4f46e5;
      font-weight: 600;
      text-decoration: none;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        color: #3730a3;
      }

      &::after {
        content: "→";
        margin-left: 8px;
        transition: transform 0.3s ease;
      }

      &:hover::after {
        transform: translateX(4px);
      }
    }
  }

  .product-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .product-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .products-section {
    padding: 80px 0;
  }

  .section-header {
    margin-bottom: 60px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
