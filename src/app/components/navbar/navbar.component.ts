import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import * as AOS from 'aos';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit {
  isMenuOpen = false;
  activeDropdown: string | null = null;
  private dropdownTimeout: any;
  isScrolled = false;

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Initialiser AOS
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100,
    });

    // Détecter le scroll pour changer l'apparence de la navbar
    this.handleScroll();
  }

  toggleMobileMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    // Empêcher le scroll du body quand le menu mobile est ouvert
    if (this.isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  showDropdown(menu: string): void {
    if (this.dropdownTimeout) {
      clearTimeout(this.dropdownTimeout);
    }
    this.activeDropdown = menu;
  }

  hideDropdown(): void {
    this.dropdownTimeout = setTimeout(() => {
      this.activeDropdown = null;
    }, 200); // Délai pour permettre la navigation dans le menu
  }

  private handleScroll(): void {
    window.addEventListener('scroll', () => {
      this.isScrolled = window.scrollY > 50;
    });
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.navbar')) {
      this.isMenuOpen = false;
      this.activeDropdown = null;
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    if (window.innerWidth > 768) {
      this.isMenuOpen = false;
    }
  }

  navigateToSection(sectionId: string): void {
    // Si nous sommes déjà sur la page d'accueil, faire défiler vers la section
    if (this.router.url === '/home' || this.router.url === '/') {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // Sinon, naviguer vers la page d'accueil avec le fragment
      this.router.navigate(['/home'], { fragment: sectionId }).then(() => {
        // Attendre que la navigation soit terminée puis faire défiler
        setTimeout(() => {
          const element = document.getElementById(sectionId);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      });
    }
  }
}
