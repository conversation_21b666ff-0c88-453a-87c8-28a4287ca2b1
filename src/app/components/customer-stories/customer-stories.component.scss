/* SECTION TÉMOIGNAGES CLIENTS STYLE SEAMLESSHR */
.customer-stories-section {
  padding: 100px 0;
  background: #f8fafc;

  @media (max-width: 768px) {
    padding: 80px 0;
  }
}

/* === EN-TÊTE STYLE SEAMLESSHR === */
.section-header {
  text-align: center;
  margin-bottom: 80px;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    margin-bottom: 60px;
  }

  .section-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    margin: 0;

    @media (max-width: 1024px) {
      font-size: 40px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }

    .highlight-text {
      color: #ff6b35;
    }
  }
}

/* === CARROUSEL DE SUCCESS STORIES === */
.stories-carousel {
  max-width: 1000px;
  margin: 0 auto;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.story-slide {
  min-width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 20px;

  @media (max-width: 768px) {
    padding: 0 10px;
  }
}

/* === CARTES DE SUCCESS STORIES === */
.story-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 48px 40px;
  text-align: center;
  transition: all 0.3s ease;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    transform: translateY(-4px);
  }

  @media (max-width: 768px) {
    padding: 32px 24px;
  }

  .company-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
    height: 80px;

    .logo-image {
      max-width: 160px;
      max-height: 80px;
      width: auto;
      height: auto;
      object-fit: contain;
      filter: grayscale(100%) opacity(0.7);
      transition: filter 0.3s ease;
    }

    @media (max-width: 768px) {
      height: 60px;
      margin-bottom: 24px;

      .logo-image {
        max-width: 120px;
        max-height: 60px;
      }
    }
  }

  &:hover .company-logo .logo-image {
    filter: grayscale(0%) opacity(1);
  }

  .story-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 24px;
    font-weight: 600;
    line-height: 1.3;
    color: #1a1a1a;
    margin-bottom: 24px;

    @media (max-width: 768px) {
      font-size: 20px;
      margin-bottom: 20px;
    }
  }

  .story-link {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: #4f46e5;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
    border: 2px solid #4f46e5;
    padding: 12px 24px;
    border-radius: 8px;

    &:hover {
      color: #ffffff;
      background: #4f46e5;
    }

    &::after {
      content: "→";
      margin-left: 8px;
      transition: transform 0.3s ease;
    }

    &:hover::after {
      transform: translateX(4px);
    }
  }
}

/* === NAVIGATION DOTS === */
.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 40px;

  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #d1d5db;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #ff6b35;
      transform: scale(1.2);
    }

    &:hover {
      background: #9ca3af;
    }

    &.active:hover {
      background: #ff6b35;
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
