import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { JobOffer, JobOffersService } from 'src/app/services/job-offer.service';

@Component({
  selector: 'app-job-detail',
  templateUrl: './job-detail.component.html',
  styleUrls: ['./job-detail.component.scss'],
})
export class JobDetailComponent implements OnInit {
  jobOffer?: JobOffer;
  similarJobs: JobOffer[] = [];
  isLoading = true;
  isJobSaved = false;
  jobId!: string;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private jobOffersService: JobOffersService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.jobId = params['id'];
      this.loadJobDetails();
    });
  }

  private loadJobDetails(): void {
    this.isLoading = true;

    this.jobOffersService.getJobOfferById(this.jobId).subscribe({
      next: (job) => {
        this.jobOffer = job;
        this.loadSimilarJobs();
        this.checkIfJobSaved();
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading job details:', err);
        this.isLoading = false;
        // Créer des données de test en cas d'erreur
        this.createMockJobData();
      },
    });
  }

  private createMockJobData(): void {
    this.jobOffer = {
      id: this.jobId,
      title: 'Développeur Full Stack Senior',
      description: `Nous recherchons un développeur Full Stack expérimenté pour rejoindre notre équipe dynamique.

      Vous serez responsable du développement et de la maintenance de nos applications web, en travaillant avec des technologies modernes comme Angular, Node.js, et MongoDB.

      Responsabilités principales :
      • Développer des applications web robustes et scalables
      • Collaborer avec l'équipe de design pour implémenter des interfaces utilisateur
      • Optimiser les performances des applications
      • Participer aux revues de code et aux décisions techniques
      • Mentorer les développeurs juniors

      Nous offrons un environnement de travail stimulant, des opportunités de formation continue, et la possibilité de travailler sur des projets innovants.`,
      publishDate: new Date().toISOString(),
      expirationDate: new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
      status: 'ACTIVE',
      location: 'Kinshasa, RDC',
      contractTypes: ['FULL_TIME'],
      minSalary: 1000,
      maxSalary: 1500,
      requiredSkills: [
        'Angular',
        'Node.js',
        'TypeScript',
        'MongoDB',
        'REST API',
        'Git',
        'Agile',
      ],
      company: {
        id: '1',
        companyName: 'LuminaHR',
        logo: 'assets/images/logo.png',
        email: '<EMAIL>',
        website: 'https://luminarh.com',
        address: {
          city: 'Kinshasa',
          country: 'RDC',
        },
      },
    };

    this.similarJobs = [
      {
        id: '2',
        title: 'Développeur Frontend',
        description: 'Développeur spécialisé en React',
        publishDate: new Date().toISOString(),
        expirationDate: new Date(
          Date.now() + 25 * 24 * 60 * 60 * 1000
        ).toISOString(),
        status: 'ACTIVE',
        location: 'Goma, RDC',
        contractTypes: ['FULL_TIME'],
        minSalary: 800,
        maxSalary: 1200,
        requiredSkills: ['React', 'JavaScript', 'CSS'],
        company: {
          id: '2',
          companyName: 'TechCorp',
          email: '<EMAIL>',
          address: {
            city: 'Goma',
            country: 'RDC',
          },
        },
      },
    ];

    this.isLoading = false;
  }

  private loadSimilarJobs(): void {
    if (!this.jobOffer) return;

    this.jobOffersService.getAllJobOffers().subscribe({
      next: (jobs) => {
        this.similarJobs = jobs
          .filter((job) => job.id !== this.jobOffer?.id)
          .slice(0, 3);
      },
      error: (err) => {
        console.error('Error loading similar jobs:', err);
      },
    });
  }

  private checkIfJobSaved(): void {
    // Vérifier si le job est sauvegardé (simulation)
    const savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');
    this.isJobSaved = savedJobs.includes(this.jobId);
  }

  getContractTypeLabel(contractType: string): string {
    return this.jobOffersService.getContractTypeLabel(contractType as any);
  }

  formatPublishDate(date: string): string {
    return this.jobOffersService.formatPublishDate(date);
  }

  formatSalary(min?: number, max?: number): string {
    return this.jobOffersService.formatSalary(min, max);
  }

  openApplyModal(): void {
    // Ouvrir le modal de candidature
    console.log('Ouvrir modal de candidature pour:', this.jobOffer?.title);
    // Ici vous pouvez implémenter l'ouverture d'un modal ou naviguer vers une page de candidature
  }

  saveJob(): void {
    const savedJobs = JSON.parse(localStorage.getItem('savedJobs') || '[]');

    if (this.isJobSaved) {
      // Retirer de la liste
      const index = savedJobs.indexOf(this.jobId);
      if (index > -1) {
        savedJobs.splice(index, 1);
      }
      this.isJobSaved = false;
    } else {
      // Ajouter à la liste
      savedJobs.push(this.jobId);
      this.isJobSaved = true;
    }

    localStorage.setItem('savedJobs', JSON.stringify(savedJobs));
  }

  navigateToJob(jobId: string): void {
    this.router.navigate(['/job', jobId]);
  }
}
