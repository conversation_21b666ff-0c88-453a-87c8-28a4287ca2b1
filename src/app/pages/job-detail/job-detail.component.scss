/* PAGE DÉTAIL EMPLOI STYLE SEAMLESSHR */
.job-detail-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* === ÉTATS DE CHARGEMENT ET ERREUR === */
.loading-container,
.not-found-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;

  .loading-spinner,
  .not-found-content {
    text-align: center;
    color: #666666;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      color: #4f46e5;

      &.fa-exclamation-triangle {
        color: #f59e0b;
      }
    }

    h2 {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 24px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 12px;
    }

    p {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      margin-bottom: 24px;
    }

    .back-btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      background: #4f46e5;
      color: #ffffff;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: #3730a3;
        transform: translateY(-2px);
      }
    }
  }
}

/* === HEADER SECTION === */
.job-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 40px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 40px;

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 32px;
    }
  }

  .job-info {
    display: flex;
    gap: 24px;
    flex: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .company-logo {
      width: 80px;
      height: 80px;
      border-radius: 12px;
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      flex-shrink: 0;

      @media (max-width: 768px) {
        width: 64px;
        height: 64px;
        margin: 0 auto;
      }

      img {
        width: 80%;
        height: 80%;
        object-fit: contain;
      }
    }

    .job-details {
      flex: 1;

      .job-title {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 32px;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 8px;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 24px;
        }
      }

      .company-info {
        .company-name {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 20px;
          font-weight: 600;
          color: #4f46e5;
          margin-bottom: 16px;

          @media (max-width: 768px) {
            font-size: 18px;
          }
        }

        .job-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 24px;

          @media (max-width: 768px) {
            justify-content: center;
            gap: 16px;
          }

          span {
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 14px;
            color: #666666;

            i {
              color: #4f46e5;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .job-actions {
    display: flex;
    gap: 16px;
    flex-shrink: 0;

    @media (max-width: 1024px) {
      width: 100%;
      justify-content: center;
    }

    @media (max-width: 768px) {
      flex-direction: column;
    }

    button {
      padding: 14px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

      i {
        font-size: 14px;
      }
    }

    .apply-btn {
      background: #4f46e5;
      color: #ffffff;
      border: none;

      &:hover {
        background: #3730a3;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
      }
    }

    .save-btn {
      background: #ffffff;
      color: #666666;
      border: 1px solid #e5e7eb;

      &:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }

      &.saved {
        color: #ef4444;
        border-color: #ef4444;

        &:hover {
          background: rgba(239, 68, 68, 0.05);
        }
      }
    }
  }
}

/* === MAIN CONTENT === */
.job-main {
  padding: 60px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
    }
  }
}

/* === CONTENU PRINCIPAL === */
.job-content {
  .content-section {
    background: #ffffff;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    border: 1px solid #e5e7eb;

    @media (max-width: 768px) {
      padding: 24px;
      margin-bottom: 24px;
    }

    .section-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 24px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 20px;

      @media (max-width: 768px) {
        font-size: 20px;
      }
    }

    .section-content {
      .job-description {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        line-height: 1.6;
        color: #374151;
        white-space: pre-line;
      }

      .skills-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .skill-tag {
          background: rgba(79, 70, 229, 0.1);
          color: #4f46e5;
          padding: 8px 16px;
          border-radius: 50px;
          font-size: 14px;
          font-weight: 500;
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        }
      }

      .company-details {
        .company-header {
          display: flex;
          gap: 20px;
          align-items: flex-start;

          @media (max-width: 768px) {
            flex-direction: column;
            text-align: center;
            gap: 16px;
          }

          .company-logo-large {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            flex-shrink: 0;

            @media (max-width: 768px) {
              margin: 0 auto;
            }

            img {
              width: 80%;
              height: 80%;
              object-fit: contain;
            }
          }

          .company-info-text {
            flex: 1;

            h4 {
              font-family: "Inter", -apple-system, BlinkMacSystemFont,
                sans-serif;
              font-size: 20px;
              font-weight: 600;
              color: #1a1a1a;
              margin-bottom: 12px;
            }

            .company-website,
            .company-address {
              font-family: "Inter", -apple-system, BlinkMacSystemFont,
                sans-serif;
              font-size: 14px;
              color: #666666;
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              gap: 8px;

              @media (max-width: 768px) {
                justify-content: center;
              }

              i {
                color: #4f46e5;
                font-size: 14px;
              }

              a {
                color: #4f46e5;
                text-decoration: none;

                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* === SIDEBAR === */
.job-sidebar {
  .sidebar-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;

    h4 {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
      margin-bottom: 16px;
    }
  }

  .apply-card {
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    color: #ffffff;
    border: none;

    h4 {
      color: #ffffff;
    }

    p {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 20px;
    }

    .apply-btn-sidebar {
      width: 100%;
      padding: 12px 16px;
      background: #ffffff;
      color: #4f46e5;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

      &:hover {
        background: #f9fafb;
        transform: translateY(-2px);
      }
    }
  }

  .summary-card {
    .summary-items {
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          color: #666666;
          font-weight: 500;
        }

        .value {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          color: #1a1a1a;
          font-weight: 600;
          text-align: right;
          max-width: 60%;
        }
      }
    }
  }

  .similar-jobs-card {
    .similar-jobs-list {
      .similar-job-item {
        padding: 16px;
        border: 1px solid #f3f4f6;
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #4f46e5;
          background: rgba(79, 70, 229, 0.02);
        }

        &:last-child {
          margin-bottom: 0;
        }

        h5 {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 4px;
          line-height: 1.3;
        }

        p {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 13px;
          color: #4f46e5;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .location {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 12px;
          color: #666666;
        }
      }
    }
  }
}
