/* === SECTION CTA FINALE STYLE SEAMLESSHR === */
.final-cta-section {
  padding: 100px 0;
  background: #ffffff;
  text-align: center;

  @media (max-width: 768px) {
    padding: 80px 0;
  }
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;

  .cta-title {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 24px;
    line-height: 1.2;

    @media (max-width: 1024px) {
      font-size: 40px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }

  .cta-description {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 18px;
    line-height: 1.6;
    color: #666666;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 16px;
      margin-bottom: 32px;
    }
  }

  .cta-actions {
    display: flex;
    justify-content: center;

    .btn-primary {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      display: inline-block;
      background: #4f46e5;
      color: #ffffff;
      padding: 16px 32px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;

      &:hover {
        background: #3730a3;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
      }

      @media (max-width: 768px) {
        padding: 14px 28px;
        font-size: 15px;
      }
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
