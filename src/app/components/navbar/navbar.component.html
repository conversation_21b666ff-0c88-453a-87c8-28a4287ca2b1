<header class="navbar-header">
  <nav class="navbar">
    <!-- Logo LuminaHR -->
    <div class="navbar-brand">
      <div class="brand-logo">
        <img src="assets/images/logo-b-w.png" alt="logo lumina" />
      </div>
    </div>

    <!-- Navigation principale -->
    <div class="navbar-nav" [class.active]="isMenuOpen">
      <div class="nav-group">
        <div
          class="nav-item has-dropdown"
          (mouseenter)="showDropdown('produit')"
          (mouseleave)="hideDropdown()"
        >
          <a href="#" class="nav-link">
            Produit
            <svg
              class="dropdown-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </a>

          <div class="dropdown-menu" *ngIf="activeDropdown === 'produit'">
            <div class="dropdown-section">
              <h4 class="dropdown-title">Fonctionnalités</h4>
              <a routerLink="/sirh" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">SIRH</span>
                  <span class="item-desc">Gestion des employés</span>
                </div>
              </a>
              <a routerLink="/paie" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">Paie</span>
                  <span class="item-desc">Calcul automatique</span>
                </div>
              </a>
              <a routerLink="/performance" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">Performance</span>
                  <span class="item-desc">Évaluations</span>
                </div>
              </a>
              <a routerLink="/recrutement" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">Recrutement</span>
                  <span class="item-desc">ATS intelligent</span>
                </div>
              </a>
              <a routerLink="/gestion-temps" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">Gestion du temps</span>
                  <span class="item-desc">Pointage et congés</span>
                </div>
              </a>
              <a routerLink="/avantages-employes" class="dropdown-item">
                <div class="item-content">
                  <span class="item-name">Avantages employés</span>
                  <span class="item-desc">Fidélisation des talents</span>
                </div>
              </a>
            </div>
          </div>
        </div>

        <a (click)="navigateToSection('benefits')" class="nav-link"
          >Solutions</a
        >
        <a (click)="navigateToSection('jobs')" class="nav-link">Emplois</a>
        <a (click)="navigateToSection('integrations')" class="nav-link"
          >Intégrations</a
        >
        <a (click)="navigateToSection('security')" class="nav-link">Sécurité</a>
        <a (click)="navigateToSection('stories')" class="nav-link"
          >Témoignages</a
        >
        <a (click)="navigateToSection('pricing')" class="nav-link"
          >Tarification</a
        >
      </div>

      <div class="navbar-actions">
        <a href="#connexion" class="btn-ghost">Se connecter</a>
        <a routerLink="/demo" class="btn-primary">Demander une démo</a>
      </div>
    </div>

    <!-- Menu mobile -->
    <button
      class="mobile-menu-toggle"
      (click)="toggleMobileMenu()"
      [class.active]="isMenuOpen"
    >
      <span></span>
      <span></span>
      <span></span>
    </button>
  </nav>
</header>
