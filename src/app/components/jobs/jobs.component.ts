import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PaginationInstance } from 'ngx-pagination';
import {
  CreateApplicationDto,
  JobOffer,
  JobOffersService,
} from 'src/app/services/job-offer.service';

@Component({
  selector: 'app-jobs',
  templateUrl: './jobs.component.html',
  styleUrls: ['./jobs.component.scss'],
})
export class JobsComponent implements OnInit {
  currentFilter = 0;
  searchTerm = '';
  page = 1;
  jobOffers: JobOffer[] = [];
  filteredOffers: JobOffer[] = [];
  isLoading = true;
  selectedOffer?: JobOffer;
  applicationData: CreateApplicationDto = {
    jobId: '',
    coverLetter: '',
    resume: '',
    references: '',
    additionalDocuments: [],
    preferredStartDate: '',
    currentEmploymentStatus: '',
    desiredSalary: undefined,
  };

  public config: PaginationInstance = {
    itemsPerPage: 6,
    currentPage: 1,
    totalItems: 0,
  };

  jobCategories = [
    { id: 0, label: 'Toutes' },
    { id: 1, label: 'Technologie' },
    { id: 2, label: 'Finance' },
    { id: 3, label: 'Santé' },
    { id: 4, label: 'Marketing' },
    { id: 5, label: 'Design' },
  ];

  constructor(
    private jobOffersService: JobOffersService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadJobOffers();
  }

  loadJobOffers(): void {
    this.isLoading = true;
    this.jobOffersService.getAllJobOffers().subscribe({
      next: (offers) => {
        this.jobOffers = offers;
        this.filteredOffers = [...this.jobOffers];
        this.config.totalItems = this.jobOffers.length;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading job offers:', err);
        this.isLoading = false;
        // Créer des données de test en cas d'erreur
        this.createMockData();
      },
    });
  }

  private createMockData(): void {
    this.jobOffers = [
      {
        id: '1',
        title: 'Développeur Full Stack',
        description: 'Nous recherchons un développeur expérimenté...',
        publishDate: new Date().toISOString(),
        expirationDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
        status: 'ACTIVE',
        location: 'Kinshasa, RDC',
        contractTypes: ['FULL_TIME'],
        minSalary: 800,
        maxSalary: 1200,
        requiredSkills: ['Angular', 'Node.js', 'TypeScript', 'MongoDB'],
        company: {
          id: '1',
          companyName: 'LuminaHR',
          logo: 'assets/images/logo.png',
          email: '<EMAIL>',
          address: {
            city: 'Kinshasa',
            country: 'RDC',
          },
        },
      },
      {
        id: '2',
        title: 'Designer UI/UX',
        description: 'Créez des expériences utilisateur exceptionnelles...',
        publishDate: new Date(
          Date.now() - 2 * 24 * 60 * 60 * 1000
        ).toISOString(),
        expirationDate: new Date(
          Date.now() + 25 * 24 * 60 * 60 * 1000
        ).toISOString(),
        status: 'ACTIVE',
        location: 'Goma, RDC',
        contractTypes: ['FULL_TIME'],
        minSalary: 600,
        maxSalary: 900,
        requiredSkills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
        company: {
          id: '2',
          companyName: 'TechCorp',
          email: '<EMAIL>',
          address: {
            city: 'Goma',
            country: 'RDC',
          },
        },
      },
      {
        id: '3',
        title: 'Spécialiste Marketing Digital',
        description: 'Développez notre présence en ligne...',
        publishDate: new Date(
          Date.now() - 5 * 24 * 60 * 60 * 1000
        ).toISOString(),
        expirationDate: new Date(
          Date.now() + 20 * 24 * 60 * 60 * 1000
        ).toISOString(),
        status: 'ACTIVE',
        location: 'Lubumbashi, RDC',
        contractTypes: ['PART_TIME'],
        minSalary: 400,
        maxSalary: 700,
        requiredSkills: ['SEO', 'Google Ads', 'Social Media', 'Analytics'],
        company: {
          id: '3',
          companyName: 'Digital Solutions',
          email: '<EMAIL>',
          address: {
            city: 'Lubumbashi',
            country: 'RDC',
          },
        },
      },
    ];
    this.filteredOffers = [...this.jobOffers];
    this.config.totalItems = this.jobOffers.length;
    this.isLoading = false;
  }

  setCurrentFilter(id: number): void {
    this.currentFilter = id;
    this.filterJobs();
  }

  filterJobs(): void {
    let filtered = this.jobOffersService.filterJobsByCategory(
      this.jobOffers,
      this.currentFilter
    );

    if (this.searchTerm.trim()) {
      filtered = this.jobOffersService.searchJobs(filtered, this.searchTerm);
    }

    this.filteredOffers = filtered;
    this.config.currentPage = 1;
  }

  getContractTypeLabel(contractType: string): string {
    return this.jobOffersService.getContractTypeLabel(contractType as any);
  }

  formatPublishDate(date: string): string {
    return this.jobOffersService.formatPublishDate(date);
  }

  formatSalary(min?: number, max?: number): string {
    return this.jobOffersService.formatSalary(min, max);
  }

  onPageChange(page: number): void {
    this.config.currentPage = page;
  }

  openDetailsModal(offer: JobOffer): void {
    this.selectedOffer = offer;
    // Ici vous pouvez ouvrir un modal ou naviguer vers une page de détails
    console.log('Ouvrir détails pour:', offer.title);
  }

  openApplyModal(offer: JobOffer): void {
    this.selectedOffer = offer;
    this.applicationData.jobId = offer.id;
    // Ici vous pouvez ouvrir un modal de candidature
    console.log('Postuler pour:', offer.title);
  }

  applyForJob(): void {
    if (!this.selectedOffer) return;

    this.jobOffersService.createApplication(this.applicationData).subscribe({
      next: () => {
        alert('Votre candidature a été soumise avec succès!');
      },
      error: (err) => {
        console.error('Erreur lors de la candidature:', err);
        alert(
          'Une erreur est survenue lors de la soumission de votre candidature.'
        );
      },
    });
  }

  onFileSelected(event: any, field: 'resume' | 'additionalDocuments'): void {
    const file: File = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        if (field === 'resume') {
          this.applicationData.resume = reader.result as string;
        } else {
          this.applicationData.additionalDocuments = [
            ...(this.applicationData.additionalDocuments || []),
            reader.result as string,
          ];
        }
      };
      reader.readAsDataURL(file);
    }
  }

  navigateToJobDetail(jobId: string): void {
    this.router.navigate(['/job', jobId]);
  }
}
