/* STYLES PARTAGÉS POUR LES PAGES DE FONCTIONNALITÉS */

/* Variables de couleurs pour chaque page */
:root {
  --sirh-primary: #4f46e5;
  --sirh-bg: #f8fafc;
  --paie-primary: #0ea5e9;
  --paie-bg: #f0f9ff;
  --performance-primary: #22c55e;
  --performance-bg: #f0fdf4;
  --recrutement-primary: #f59e0b;
  --recrutement-bg: #fffbeb;
  --temps-primary: #8b5cf6;
  --temps-bg: #faf5ff;
  --avantages-primary: #ef4444;
  --avantages-bg: #fef2f2;
}

/* Base commune pour toutes les pages */
.feature-page {
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }
}

/* === HERO SECTION === */
.feature-hero {
  padding: 120px 0 80px;

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .hero-text {
    .hero-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      color: #1a1a1a;
      margin-bottom: 24px;

      @media (max-width: 1024px) {
        font-size: 40px;
      }

      @media (max-width: 768px) {
        font-size: 32px;
      }
    }

    .hero-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 20px;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 40px;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }

    .hero-actions {
      display: flex;
      gap: 20px;

      @media (max-width: 1024px) {
        justify-content: center;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
      }

      .cta-primary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: #4f46e5;
        color: #ffffff;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #3730a3;
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        i {
          font-size: 14px;
        }
      }

      .cta-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: #ffffff;
        color: #4f46e5;
        text-decoration: none;
        border: 2px solid #4f46e5;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #4f46e5;
          color: #ffffff;
        }
      }
    }
  }

  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .hero-image {
      width: 100%;
      max-width: 600px;
      height: auto;
      border-radius: 16px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    }
  }
}

/* === FEATURES SECTION === */
.feature-features {
  padding: 100px 0;
  background: #f8fafc;

  .section-header {
    text-align: center;
    margin-bottom: 80px;

    .section-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 16px;

      @media (max-width: 768px) {
        font-size: 32px;
      }
    }

    .section-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      line-height: 1.6;
      color: #666666;
      max-width: 600px;
      margin: 0 auto;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .feature-card {
      background: #ffffff;
      border-radius: 16px;
      padding: 32px;
      text-align: center;
      border: 1px solid #e5e7eb;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
        border-color: #d1d5db;
      }

      .feature-icon {
        width: 64px;
        height: 64px;
        background: rgba(79, 70, 229, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;

        i {
          font-size: 24px;
          color: #4f46e5;
        }
      }

      .feature-title {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 20px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 12px;
      }

      .feature-description {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        line-height: 1.6;
        color: #666666;
      }
    }
  }
}

/* === BENEFITS SECTION === */
.feature-benefits {
  padding: 100px 0;
  background: #ffffff;

  .benefits-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
    }
  }

  .benefits-text {
    .benefits-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 40px;

      @media (max-width: 768px) {
        font-size: 32px;
        text-align: center;
      }
    }

    .benefits-list {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .benefit-item {
        display: flex;
        gap: 16px;
        align-items: flex-start;

        i {
          color: #4f46e5;
          font-size: 20px;
          margin-top: 4px;
          flex-shrink: 0;
        }

        .benefit-content {
          h4 {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
          }

          p {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #666666;
            margin: 0;
          }
        }
      }
    }
  }

  .benefits-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    @media (max-width: 1024px) {
      order: -1;
    }

    .benefits-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 16px;
    }
  }
}

/* === CTA SECTION === */
.feature-cta {
  padding: 80px 0;
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  color: #ffffff;

  .cta-content {
    text-align: center;

    .cta-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 16px;

      @media (max-width: 768px) {
        font-size: 32px;
      }
    }

    .cta-subtitle {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 40px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }

    .cta-actions {
      display: flex;
      gap: 20px;
      justify-content: center;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
        align-items: center;
      }

      .cta-primary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: #ffffff;
        color: #4f46e5;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #f9fafb;
          transform: translateY(-2px);
        }
      }

      .cta-secondary {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 16px 32px;
        background: transparent;
        color: #ffffff;
        text-decoration: none;
        border: 2px solid #ffffff;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;

        &:hover {
          background: #ffffff;
          color: #4f46e5;
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}
