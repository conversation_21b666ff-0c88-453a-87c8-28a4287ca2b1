import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { DemoRequestComponent } from './pages/demo-request/demo-request.component';
import { JobDetailComponent } from './pages/job-detail/job-detail.component';
import { PayrollSoftwareComponent } from './pages/payroll-software/payroll-software.component';
import { SirhComponent } from './pages/sirh/sirh.component';
import { PaieComponent } from './pages/paie/paie.component';
import { PerformanceComponent } from './pages/performance/performance.component';
import { RecrutementComponent } from './pages/recrutement/recrutement.component';
import { GestionTempsComponent } from './pages/gestion-temps/gestion-temps.component';
import { AvantagesEmployesComponent } from './pages/avantages-employes/avantages-employes.component';

const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: 'home', component: HomeComponent },
  { path: 'demo', component: DemoRequestComponent },
  { path: 'job/:id', component: JobDetailComponent },
  { path: 'sirh', component: SirhComponent },
  { path: 'paie', component: PaieComponent },
  { path: 'performance', component: PerformanceComponent },
  { path: 'recrutement', component: RecrutementComponent },
  { path: 'gestion-temps', component: GestionTempsComponent },
  { path: 'avantages-employes', component: AvantagesEmployesComponent },
  { path: 'payroll-software', component: PayrollSoftwareComponent },
  { path: '**', redirectTo: '/home' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'top',
      anchorScrolling: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
