.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;
  padding: 160px 0 80px;

  @media (max-width: 1024px) {
    padding: 140px 0 70px;
  }

  @media (max-width: 768px) {
    min-height: 90vh;
    padding: 130px 0 60px;
  }

  @media (max-width: 480px) {
    min-height: 80vh;
    padding: 120px 0 40px;
  }

  // Arrière-plan animé moderne
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.3;
      animation: float 8s ease-in-out infinite;

      &.orb-1 {
        width: 300px;
        height: 300px;
        background: linear-gradient(45deg, #6366f1, #8b5cf6);
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg, #06b6d4, #3b82f6);
        top: 60%;
        right: 20%;
        animation-delay: 3s;
      }

      &.orb-3 {
        width: 150px;
        height: 150px;
        background: linear-gradient(45deg, #ec4899, #f59e0b);
        bottom: 20%;
        left: 30%;
        animation-delay: 6s;
      }
    }

    .floating-elements {
      position: absolute;
      width: 100%;
      height: 100%;

      .floating-element {
        position: absolute;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        animation: floatSlow 12s ease-in-out infinite;

        &.element-1 {
          top: 15%;
          left: 15%;
          animation-delay: 0s;
        }

        &.element-2 {
          top: 25%;
          right: 25%;
          animation-delay: 3s;
        }

        &.element-3 {
          bottom: 35%;
          left: 25%;
          animation-delay: 6s;
        }

        &.element-4 {
          bottom: 15%;
          right: 15%;
          animation-delay: 9s;
        }
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }

    @media (max-width: 480px) {
      padding: 0 16px;
    }
  }

  // Layout centré style Strettch
  .hero-content-centered {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
    height: 100%; // Prend toute la hauteur
    justify-content: space-between; // Distribue l'espace
  }

  .hero-text-section {
    margin-bottom: 60px;
    flex-shrink: 0; // Ne se réduit pas

    @media (max-width: 768px) {
      margin-bottom: 40px;
    }

    @media (max-width: 480px) {
      margin-bottom: 30px;
    }
  }

  .hero-visual-section {
    margin-bottom: 0; // Pas de marge - va jusqu'en bas
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
    flex: 1; // Prend tout l'espace disponible
    display: flex;
    align-items: flex-end; // Aligne vers le bas

    @media (max-width: 768px) {
      padding: 0 16px;
    }

    @media (max-width: 480px) {
      padding: 0 12px;
    }
  }

  .hero-text-section {
    .hero-badge {
      display: inline-block;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      font-size: 14px;
      font-weight: 500;
      color: white;
      margin-bottom: 32px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
      }
    }

    .hero-title {
      font-size: 4.5rem; // Plus grand comme Strettch
      font-weight: 800;
      line-height: 1.1;
      color: white;
      margin-bottom: 32px;
      letter-spacing: -0.02em;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      max-width: 900px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 1024px) {
        font-size: 3.75rem;
      }

      @media (max-width: 768px) {
        font-size: 3rem;
        margin-bottom: 24px;
      }

      @media (max-width: 480px) {
        font-size: 2.25rem;
        line-height: 1.2;
      }
    }

    .hero-subtitle {
      font-size: 1.375rem; // Plus grand
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.85);
      margin-bottom: 48px;
      max-width: 700px; // Plus large pour le layout centré
      font-weight: 400;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 1.25rem;
        margin-bottom: 40px;
        max-width: 100%;
      }

      @media (max-width: 480px) {
        font-size: 1.125rem;
        line-height: 1.5;
        margin-bottom: 32px;
      }
    }

    .hero-actions {
      display: flex;
      justify-content: center;
      margin-bottom: 0; // Pas de marge car on a la section visuelle après

      .btn-primary,
      .btn-secondary {
        padding: 16px 32px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;

        @media (max-width: 768px) {
          padding: 14px 24px;
          font-size: 15px;
        }

        @media (max-width: 480px) {
          padding: 12px 20px;
          font-size: 14px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }

      .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 18px 36px;
        font-size: 16px;

        &:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
          box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
        }

        @media (max-width: 768px) {
          padding: 16px 32px;
          font-size: 15px;
        }

        @media (max-width: 480px) {
          padding: 14px 28px;
          font-size: 14px;
        }
      }

      .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  // Section visuelle centrée - Style Strettch
  .hero-visual-section {
    width: 100%;
    max-width: 1200px; // Plus large
    margin: 0 auto;

    .dashboard-mockup {
      position: relative;
      border-radius: 24px 24px 0 0; // Coins arrondis seulement en haut
      overflow: hidden;
      width: 100%;
      height: 600px; // Hauteur fixe plus importante
      // Bordures stylisées comme Strettch
      background: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0.05)
      );
      backdrop-filter: blur(20px);
      border: 2px solid;
      border-bottom: none; // Pas de bordure en bas pour aller jusqu'au bout
      border-image: linear-gradient(
          145deg,
          rgba(255, 255, 255, 0.3),
          rgba(255, 255, 255, 0.1)
        )
        1;
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

      // Effet de glow subtil
      &::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(
          145deg,
          rgba(99, 102, 241, 0.3),
          rgba(59, 130, 246, 0.2)
        );
        border-radius: 26px;
        z-index: -1;
        opacity: 0.6;
        filter: blur(8px);
      }

      .dashboard-image {
        width: 100%;
        height: 100%; // Prend toute la hauteur du conteneur
        display: block;
        border-radius: 22px 22px 0 0; // Coins arrondis seulement en haut
        transition: all 0.4s ease;
        position: relative;
        z-index: 1;
        object-fit: cover; // Garde les proportions et remplit l'espace
        object-position: top; // Affiche le haut de l'image

        &:hover {
          transform: scale(1.01) translateY(-2px);
        }
      }

      @media (max-width: 1024px) {
        max-width: 900px;
        height: 500px;
        border-radius: 20px 20px 0 0;

        &::before {
          border-radius: 22px 22px 0 0;
        }

        .dashboard-image {
          border-radius: 18px 18px 0 0;
        }
      }

      @media (max-width: 768px) {
        height: 400px;
        border-radius: 16px 16px 0 0;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.35),
          0 0 0 1px rgba(255, 255, 255, 0.1);

        &::before {
          border-radius: 18px 18px 0 0;
          filter: blur(6px);
        }

        .dashboard-image {
          border-radius: 14px 14px 0 0;
        }
      }

      @media (max-width: 480px) {
        height: 300px;
        border-radius: 12px 12px 0 0;

        &::before {
          border-radius: 14px 14px 0 0;
        }

        .dashboard-image {
          border-radius: 10px 10px 0 0;
        }
      }
    }
  }

  // Stats repositionnées en bas
  .hero-stats {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 32px;

      margin-bottom: 32px;

      @media (max-width: 640px) {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .stat-item {
        position: relative;

        .stat-number {
          font-size: 2.5rem;
          font-weight: 900;

          color: white;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          text-transform: uppercase;
          letter-spacing: 1px;
          font-weight: 500;
          margin-bottom: 12px;
        }

        .stat-progress {
          width: 100%;
          height: 3px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
          overflow: hidden;

          .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            border-radius: 2px;
            width: 0;
            animation: progressFill 2s ease-out forwards;
            animation-delay: calc(var(--index, 0) * 0.3s + 1s);
          }
        }
      }
    }

    .trust-badges {
      display: flex;
      align-items: center;
      gap: 24px;
      padding-top: 32px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      @media (max-width: 640px) {
        flex-direction: column;
        gap: 16px;
      }

      .trust-text {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
      }

      .badges {
        display: flex;
        gap: 16px;

        .badge-item {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          font-size: 12px;
          font-weight: 600;
          color: white;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// Visuel professionnel épuré
.hero-visual {
  position: relative;

  .dashboard-mockup {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;

    &:hover {
      transform: perspective(1000px) rotateY(-2deg) rotateX(2deg);
    }

    .dashboard-image {
      width: 100%;
      height: auto;
      display: block;
    }
  }

  @media (max-width: 1024px) {
    .dashboard-mockup {
      transform: none;

      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

// Animations modernes
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(2deg);
  }
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-25px) translateX(15px) scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes progressFill {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes dashboardFloat {
  0%,
  100% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(0px);
  }
  50% {
    transform: rotateY(-5deg) rotateX(5deg) translateY(-10px);
  }
}

@keyframes cardPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

@keyframes barGrow {
  0% {
    transform: scaleY(0);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes floatCard {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-15px) rotate(1deg);
    opacity: 1;
  }
}
