/* === FOOTER STYLE SEAMLESSHR === */
.footer {
  background: #1e293b;
  color: #ffffff;
}

/* === SECTION PRINCIPALE DU FOOTER === */
.footer-main {
  padding: 80px 0 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  @media (max-width: 768px) {
    gap: 40px;
  }
}

/* === SECTION GAUCHE: NEWSLETTER ET SOCIAL === */
.footer-left {
  .newsletter-section {
    margin-bottom: 40px;

    .newsletter-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 16px;
    }

    .newsletter-form {
      display: flex;
      gap: 0;
      background: #334155;
      border-radius: 8px;
      padding: 4px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 8px;
        padding: 8px;
      }

      .newsletter-input {
        flex: 1;
        padding: 12px 16px;
        border: none;
        background: transparent;
        color: #ffffff;
        font-size: 14px;

        &::placeholder {
          color: #94a3b8;
        }

        &:focus {
          outline: none;
        }

        @media (max-width: 768px) {
          background: #475569;
          border-radius: 6px;
        }
      }

      .newsletter-btn {
        padding: 12px 24px;
        background: #4f46e5;
        color: #ffffff;
        border: none;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;

        &:hover {
          background: #3730a3;
        }
      }
    }
  }

  .social-section {
    .social-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #94a3b8;
      margin-bottom: 16px;
      letter-spacing: 0.1em;
    }

    .social-icons {
      display: flex;
      gap: 12px;

      .social-link {
        width: 36px;
        height: 36px;
        background: #334155;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #94a3b8;
        transition: all 0.3s ease;

        &:hover {
          background: #475569;
          color: #ffffff;
        }

        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

/* === SECTION DROITE: COLONNES DE LIENS === */
.footer-right {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer-column {
    .footer-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #94a3b8;
      margin-bottom: 20px;
      letter-spacing: 0.05em;
    }

    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 12px;

        a {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          color: #ffffff;
          text-decoration: none;
          font-size: 14px;
          transition: all 0.3s ease;

          &:hover {
            color: #4f46e5;
          }
        }
      }
    }
  }
}

/* === SECTION DU BAS === */
.footer-bottom {
  background: #0f172a;
  padding: 40px 0 20px;
  border-top: 1px solid #334155;
}

.footer-bottom-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 60px;
  align-items: start;
  margin-bottom: 40px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

/* === BUREAUX === */
.footer-offices {
  display: flex;
  gap: 60px;

  @media (max-width: 1024px) {
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 32px;
  }

  .office-section {
    .office-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #94a3b8;
      margin-bottom: 12px;
      letter-spacing: 0.05em;
    }

    .office-address {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      color: #ffffff;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 8px;
    }

    .office-email {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      color: #ffffff;
      font-size: 14px;
      margin: 0;
    }
  }

  .global-section {
    .global-title {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 600;
      color: #94a3b8;
      margin-bottom: 12px;
      letter-spacing: 0.05em;
    }

    .country-flags {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      @media (max-width: 768px) {
        justify-content: center;
      }

      .flag {
        width: 24px;
        height: 18px;
        border-radius: 3px;
        opacity: 0.8;
        transition: all 0.3s ease;

        &:hover {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }
  }
}

/* === CERTIFICATION ISO === */
.footer-certification {
  display: flex;
  justify-content: flex-end;

  @media (max-width: 1024px) {
    justify-content: center;
  }

  .iso-badge {
    .iso-image {
      height: 80px;
      width: auto;
      opacity: 0.9;
      transition: all 0.3s ease;

      &:hover {
        opacity: 1;
        transform: scale(1.05);
      }
    }
  }
}

/* === COPYRIGHT === */
.footer-copyright {
  border-top: 1px solid #334155;
  padding-top: 20px;

  .copyright-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .copyright-text {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      color: #94a3b8;
      font-size: 14px;
      margin: 0;
    }

    .legal-links {
      display: flex;
      gap: 24px;

      @media (max-width: 768px) {
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
      }

      .legal-link {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        color: #94a3b8;
        text-decoration: none;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          color: #ffffff;
        }
      }
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .footer-main {
    padding: 60px 0 40px;
  }

  .footer-bottom {
    padding: 32px 0 16px;
  }
}
