<!-- PAGE DÉTAIL EMPLOI -->
<div class="job-detail-page">
  <app-navbar></app-navbar>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Chargement de l'offre...</p>
    </div>
  </div>

  <!-- Job Not Found -->
  <div *ngIf="!isLoading && !jobOffer" class="not-found-container">
    <div class="not-found-content">
      <i class="fas fa-exclamation-triangle"></i>
      <h2>Offre d'emploi introuvable</h2>
      <p>L'offre que vous recherchez n'existe pas ou a été supprimée.</p>
      <a routerLink="/home#jobs" class="back-btn">Retour aux offres</a>
    </div>
  </div>

  <!-- Job Detail Content -->
  <div *ngIf="!isLoading && jobOffer" class="job-detail-content">
    <!-- Header Section -->
    <section class="job-header">
      <div class="container">
        <div class="header-content">
          <div class="job-info">
            <div class="company-logo">
              <img
                [src]="
                  jobOffer.company.logo || 'assets/images/default-company.png'
                "
                [alt]="jobOffer.company.companyName"
              />
            </div>
            <div class="job-details">
              <h1 class="job-title">{{ jobOffer.title }}</h1>
              <div class="company-info">
                <h2 class="company-name">{{ jobOffer.company.companyName }}</h2>
                <div class="job-meta">
                  <span class="location">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ jobOffer.location }}
                  </span>
                  <span class="contract-type">
                    <i class="fas fa-briefcase"></i>
                    {{ getContractTypeLabel(jobOffer.contractTypes[0]) }}
                  </span>
                  <span
                    class="salary"
                    *ngIf="jobOffer.minSalary || jobOffer.maxSalary"
                  >
                    <i class="fas fa-coins"></i>
                    {{ formatSalary(jobOffer.minSalary, jobOffer.maxSalary) }}
                  </span>
                  <span class="publish-date">
                    <i class="fas fa-calendar"></i>
                    Publié {{ formatPublishDate(jobOffer.publishDate) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="job-actions">
            <button class="apply-btn" (click)="openApplyModal()">
              <i class="fas fa-paper-plane"></i>
              Postuler maintenant
            </button>
            <button
              class="save-btn"
              (click)="saveJob()"
              [class.saved]="isJobSaved"
            >
              <i
                class="fas fa-heart"
                [class.fas]="isJobSaved"
                [class.far]="!isJobSaved"
              ></i>
              {{ isJobSaved ? "Sauvegardé" : "Sauvegarder" }}
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <section class="job-main">
      <div class="container">
        <div class="main-content">
          <!-- Left Column: Job Details -->
          <div class="job-content">
            <!-- Description -->
            <div class="content-section">
              <h3 class="section-title">Description du poste</h3>
              <div class="section-content">
                <p class="job-description">{{ jobOffer.description }}</p>
              </div>
            </div>

            <!-- Required Skills -->
            <div
              class="content-section"
              *ngIf="jobOffer.requiredSkills.length > 0"
            >
              <h3 class="section-title">Compétences requises</h3>
              <div class="section-content">
                <div class="skills-grid">
                  <span
                    *ngFor="let skill of jobOffer.requiredSkills"
                    class="skill-tag"
                  >
                    {{ skill }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Company Information -->
            <div class="content-section">
              <h3 class="section-title">À propos de l'entreprise</h3>
              <div class="section-content">
                <div class="company-details">
                  <div class="company-header">
                    <div class="company-logo-large">
                      <img
                        [src]="
                          jobOffer.company.logo ||
                          'assets/images/default-company.png'
                        "
                        [alt]="jobOffer.company.companyName"
                      />
                    </div>
                    <div class="company-info-text">
                      <h4>{{ jobOffer.company.companyName }}</h4>
                      <p
                        *ngIf="jobOffer.company.website"
                        class="company-website"
                      >
                        <i class="fas fa-globe"></i>
                        <a [href]="jobOffer.company.website" target="_blank">{{
                          jobOffer.company.website
                        }}</a>
                      </p>
                      <p
                        *ngIf="jobOffer.company.address"
                        class="company-address"
                      >
                        <i class="fas fa-map-marker-alt"></i>
                        {{ jobOffer.company.address.city }},
                        {{ jobOffer.company.address.country }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column: Sidebar -->
          <div class="job-sidebar">
            <!-- Quick Apply Card -->
            <div class="sidebar-card apply-card">
              <h4>Postuler rapidement</h4>
              <p>Intéressé par ce poste ? Postulez dès maintenant !</p>
              <button class="apply-btn-sidebar" (click)="openApplyModal()">
                <i class="fas fa-paper-plane"></i>
                Postuler
              </button>
            </div>

            <!-- Job Summary -->
            <div class="sidebar-card summary-card">
              <h4>Résumé de l'offre</h4>
              <div class="summary-items">
                <div class="summary-item">
                  <span class="label">Type de contrat</span>
                  <span class="value">{{
                    getContractTypeLabel(jobOffer.contractTypes[0])
                  }}</span>
                </div>
                <div
                  class="summary-item"
                  *ngIf="jobOffer.minSalary || jobOffer.maxSalary"
                >
                  <span class="label">Salaire</span>
                  <span class="value">{{
                    formatSalary(jobOffer.minSalary, jobOffer.maxSalary)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Localisation</span>
                  <span class="value">{{ jobOffer.location }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Date de publication</span>
                  <span class="value">{{
                    formatPublishDate(jobOffer.publishDate)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="label">Date d'expiration</span>
                  <span class="value">{{
                    jobOffer.expirationDate | date : "dd/MM/yyyy"
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Similar Jobs -->
            <div
              class="sidebar-card similar-jobs-card"
              *ngIf="similarJobs.length > 0"
            >
              <h4>Offres similaires</h4>
              <div class="similar-jobs-list">
                <div
                  *ngFor="let job of similarJobs"
                  class="similar-job-item"
                  (click)="navigateToJob(job.id)"
                >
                  <h5>{{ job.title }}</h5>
                  <p>{{ job.company.companyName }}</p>
                  <span class="location">{{ job.location }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
